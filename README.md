# 清云互联 - AI代码助手 CodeBuddy 官网

这是一个现代化的响应式网站，展示清云互联的AI代码助手CodeBuddy产品。网站采用HTML、CSS、JavaScript基础框架构建，具有视频轮播功能和优雅的用户界面。

## 🌟 主要特性

### 🎥 视频轮播功能
- **自动播放**: 支持8秒间隔自动切换视频
- **手动控制**: 提供播放/暂停、上一个/下一个控制按钮
- **指示器导航**: 点击圆点指示器快速跳转到指定视频
- **键盘控制**: 支持空格键播放/暂停，左右箭头键切换视频
- **鼠标交互**: 鼠标悬停时暂停自动播放，移开后恢复
- **预加载优化**: 预加载视频以提供流畅的用户体验

### 🎨 界面设计
- **现代化UI**: 采用渐变背景和毛玻璃效果
- **响应式设计**: 完美适配桌面、平板和手机设备
- **流畅动画**: 丰富的CSS3动画和过渡效果
- **交互反馈**: 悬停效果和点击反馈

### ⚙️ 技术特点
- **配置化管理**: 所有内容通过config.js统一管理，避免硬编码
- **模块化设计**: 代码结构清晰，易于维护和扩展
- **错误处理**: 完善的错误处理机制
- **性能优化**: 视频预加载和懒加载技术

## 📁 项目结构

```
清云互联官网/
├── index.html          # 主页面文件
├── styles.css          # 样式文件
├── script.js           # 主要JavaScript逻辑
├── config.js           # 配置文件
└── README.md           # 项目说明文档
```

## 🚀 快速开始

### 1. 下载项目
将所有文件下载到本地目录

### 2. 启动项目
由于项目使用了外部视频资源，建议使用本地服务器运行：

**使用Python (推荐)**:
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

**使用Node.js**:
```bash
npx http-server
```

**使用PHP**:
```bash
php -S localhost:8000
```

### 3. 访问网站
在浏览器中打开 `http://localhost:8000`

## 🎮 使用说明

### 视频轮播控制
- **自动播放**: 页面加载后视频自动开始播放和轮播
- **播放/暂停**: 点击视频或播放按钮控制播放状态
- **切换视频**: 使用左右箭头按钮或键盘方向键
- **快速跳转**: 点击底部圆点指示器
- **键盘快捷键**:
  - `空格键`: 播放/暂停
  - `←`: 上一个视频
  - `→`: 下一个视频

### 响应式适配
- **桌面端**: 完整功能和最佳视觉效果
- **平板端**: 自动调整布局，保持良好体验
- **手机端**: 简化导航，优化触摸操作

## ⚙️ 配置说明

### 修改品牌信息
编辑 `config.js` 文件中的 `brand` 部分：
```javascript
brand: {
    name: "您的品牌名称",
    logo: "fas fa-your-icon",
    slogan: "您的标语",
    description: "您的描述"
}
```

### 更新视频列表
编辑 `config.js` 文件中的 `videoCarousel.videos` 部分：
```javascript
videos: [
    {
        url: "您的视频URL",
        title: "视频标题",
        description: "视频描述"
    }
    // 添加更多视频...
]
```

### 调整轮播设置
编辑 `config.js` 文件中的 `videoCarousel.settings` 部分：
```javascript
settings: {
    autoPlayInterval: 8000,    // 自动切换间隔(毫秒)
    fadeTransitionDuration: 500, // 过渡动画时长
    preloadNext: true,         // 是否预加载下一个视频
    showControls: true,        // 是否显示控制按钮
    pauseOnHover: true         // 鼠标悬停时是否暂停
}
```

### 修改服务卡片
编辑 `config.js` 文件中的 `services` 部分来添加或修改服务卡片。

## 🎨 自定义样式

### 主题颜色
在 `config.js` 的 `theme` 部分修改主题颜色：
```javascript
theme: {
    primaryColor: "#1976d2",     // 主色调
    secondaryColor: "#667eea",   // 次要颜色
    accentColor: "#764ba2",      // 强调色
    // ...其他颜色配置
}
```

### CSS变量
主要的CSS变量定义在 `styles.css` 中，可以通过修改这些变量来快速调整整体样式。

## 🔧 技术栈

- **HTML5**: 语义化标签和现代HTML特性
- **CSS3**: Flexbox、Grid、动画和响应式设计
- **JavaScript ES6+**: 模块化、类、箭头函数等现代特性
- **Font Awesome**: 图标库
- **配置化设计**: 通过JSON配置管理内容

## 📱 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- 移动端浏览器

## 🚨 注意事项

1. **视频资源**: 确保视频URL可访问，建议使用HTTPS协议
2. **自动播放**: 某些浏览器可能阻止视频自动播放，需要用户交互后才能播放
3. **网络环境**: 视频加载速度取决于网络环境和视频文件大小
4. **跨域问题**: 如果视频资源存在跨域限制，可能需要配置CORS

## 🔄 更新日志

### v1.0.0 (2024-01-01)
- ✨ 初始版本发布
- 🎥 实现视频轮播功能
- 🎨 完成响应式设计
- ⚙️ 添加配置化管理
- 📱 优化移动端体验

## 📞 技术支持

如果您在使用过程中遇到问题，请检查：
1. 浏览器控制台是否有错误信息
2. 网络连接是否正常
3. 视频URL是否可访问
4. 浏览器是否支持HTML5视频

## 📄 许可证

本项目仅供学习和参考使用。
