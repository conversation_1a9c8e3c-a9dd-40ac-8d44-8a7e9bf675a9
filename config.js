// 网站配置文件 - 集中管理所有配置信息，避免硬编码

const SITE_CONFIG = {
    // 品牌信息
    brand: {
        name: "清云互联",
        logo: "fas fa-cloud",
        slogan: "AI代码助手 CodeBuddy 全新发布",
        description: "从此告别繁琐编程，AI 自主完成多文件代码生成和优化，让您的开发更高效"
    },

    // 导航菜单配置
    navigation: {
        leftMenu: [
            { text: "备案专区", href: "#", hasHotTag: true },
            { text: "备案方案", href: "#" },
            { text: "学习", href: "#" },
            { text: "企业中心", href: "#" },
            { text: "云市场", href: "#" },
            { text: "开发者", href: "#" },
            { text: "客户支持", href: "#" },
            { text: "合作与生态", href: "#" },
            { text: "了解清云互联", href: "#" }
        ],
        rightActions: [
            { text: "中国站", href: "#" },
            { text: "文档", href: "#" },
            { text: "备案", href: "#" },
            { text: "控制台", href: "#" }
        ]
    },

    // 侧边栏配置
    sidebar: [
        { 
            icon: "fas fa-robot", 
            text: "AI 更新", 
            subtext: "智能体平台", 
            active: true 
        },
        { 
            text: "AI代码助手 CodeBuddy", 
            hasNewTag: true 
        },
        { 
            text: "轻量云7月活动火爆" 
        },
        { 
            text: "游戏联机服务器备案专场" 
        },
        { 
            text: "大模型机器人全新发布" 
        }
    ],

    // 视频轮播配置
    videoCarousel: {
        videos: [
            {
                url: "https://ctyun-portal.gdoss.xstore.ctyun.cn/activity_media/a9eeb63e607c4e7aaf708c3458df1128.mp4",
                title: "清云互联产品介绍",
                description: "了解清云互联的核心产品和服务"
            },
            {
                url: "https://ctyun-portal.gdoss.xstore.ctyun.cn/activity_media/bc1d3bd8b902499c87e78fd3933b33a3.mp4",
                title: "AI代码助手演示",
                description: "体验AI代码助手的强大功能"
            },
            {
                url: "https://ctyun-portal.gdoss.xstore.ctyun.cn/activity_media/e79bed7ac8b1430b8521990c769059c1.mp4",
                title: "云服务解决方案",
                description: "全面的云计算解决方案"
            },
            {
                url: "https://ctyun-portal.gdoss.xstore.ctyun.cn/activity_media/90598f132de64875a8a8463fdfa5efff.m4v",
                title: "企业数字化转型",
                description: "助力企业实现数字化转型"
            },
            {
                url: "https://tongji.baidu.com/web5/image/banner/login_ab.mp4",
                title: "数据分析平台",
                description: "强大的数据分析和可视化工具"
            },
            {
                url: "https://tongji.baidu.com/web5/image/banner/login_new.mp4",
                title: "智能运维系统",
                description: "自动化运维管理解决方案"
            }
        ],
        settings: {
            autoPlayInterval: 8000, // 8秒自动切换
            fadeTransitionDuration: 500, // 淡入淡出时间
            preloadNext: true, // 预加载下一个视频
            showControls: true, // 显示控制按钮
            showIndicators: true, // 显示指示器
            pauseOnHover: true, // 鼠标悬停时暂停
            keyboardControl: true // 键盘控制
        }
    },

    // 服务卡片配置
    services: [
        {
            icon: "fas fa-shield-alt",
            title: "云服务器 CVM",
            description: "提供安全可靠的弹性计算服务",
            link: "#",
            color: "blue"
        },
        {
            icon: "fas fa-mobile-alt",
            title: "免费应用",
            description: "清云互联提供 50+ 款产品免费试用",
            link: "#",
            color: "blue"
        },
        {
            icon: "fas fa-search",
            title: "DeepSeek 专题活动",
            description: "多种开发套餐，MCP 一键部署",
            link: "#",
            color: "blue"
        },
        {
            icon: "fas fa-globe",
            title: "云官网建站",
            description: "无需代码，建站快速简单",
            link: "#",
            color: "blue"
        },
        {
            icon: "fas fa-code",
            title: "向量数据库2.0+Dify",
            description: "零代码实现AI Agent智能体",
            link: "#",
            color: "blue"
        }
    ],

    // 主题配置
    theme: {
        primaryColor: "#1976d2",
        secondaryColor: "#667eea",
        accentColor: "#764ba2",
        backgroundColor: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
        textColor: "#333",
        lightTextColor: "#666",
        whiteColor: "#ffffff",
        borderRadius: "8px",
        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)"
    },

    // 响应式断点
    breakpoints: {
        mobile: "480px",
        tablet: "768px",
        desktop: "1024px",
        large: "1200px"
    },

    // 动画配置
    animations: {
        duration: {
            fast: "0.2s",
            normal: "0.3s",
            slow: "0.6s"
        },
        easing: {
            ease: "ease",
            easeInOut: "ease-in-out",
            easeOut: "ease-out"
        }
    },

    // SEO配置
    seo: {
        title: "清云互联 - AI代码助手 CodeBuddy",
        description: "清云互联提供强大的AI代码助手CodeBuddy，助力开发者提高编程效率，实现智能化代码生成和优化。",
        keywords: "清云互联,AI代码助手,CodeBuddy,云计算,人工智能,代码生成",
        author: "清云互联",
        viewport: "width=device-width, initial-scale=1.0"
    },

    // API配置（如果需要）
    api: {
        baseUrl: "/api",
        timeout: 10000,
        retryAttempts: 3
    },

    // 功能开关
    features: {
        videoAutoplay: true,
        lazyLoading: true,
        analytics: false,
        chatbot: false,
        newsletter: false
    }
};

// 导出配置（如果使用模块化）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SITE_CONFIG;
}

// 全局配置访问器
window.SITE_CONFIG = SITE_CONFIG;
