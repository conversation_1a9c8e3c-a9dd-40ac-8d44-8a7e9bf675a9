// 配置文件 - 避免硬编码
const CONFIG = {
    brand: {
        name: "清云互联",
        logo: "fas fa-cloud"
    },
    videos: [
        {
            url: "https://ctyun-portal.gdoss.xstore.ctyun.cn/activity_media/a9eeb63e607c4e7aaf708c3458df1128.mp4",
            title: "视频1"
        },
        {
            url: "https://ctyun-portal.gdoss.xstore.ctyun.cn/activity_media/bc1d3bd8b902499c87e78fd3933b33a3.mp4",
            title: "视频2"
        },
        {
            url: "https://ctyun-portal.gdoss.xstore.ctyun.cn/activity_media/e79bed7ac8b1430b8521990c769059c1.mp4",
            title: "视频3"
        },
        {
            url: "https://ctyun-portal.gdoss.xstore.ctyun.cn/activity_media/90598f132de64875a8a8463fdfa5efff.m4v",
            title: "视频4"
        },
        {
            url: "https://tongji.baidu.com/web5/image/banner/login_ab.mp4",
            title: "视频5"
        },
        {
            url: "https://tongji.baidu.com/web5/image/banner/login_new.mp4",
            title: "视频6"
        }
    ],
    autoPlayInterval: 8000, // 8秒自动切换
    fadeTransitionDuration: 500 // 淡入淡出时间
};

class VideoCarousel {
    constructor() {
        this.currentIndex = 0;
        this.isPlaying = true;
        this.autoPlayTimer = null;
        this.video = document.getElementById('mainVideo');
        this.playPauseBtn = document.getElementById('playPauseBtn');
        this.prevBtn = document.getElementById('prevBtn');
        this.nextBtn = document.getElementById('nextBtn');
        this.indicatorsContainer = document.querySelector('.video-indicators');
        
        this.init();
    }

    init() {
        this.createIndicators();
        this.loadVideo(0);
        this.bindEvents();
        this.startAutoPlay();
        this.preloadVideos();
    }

    createIndicators() {
        this.indicatorsContainer.innerHTML = '';
        CONFIG.videos.forEach((_, index) => {
            const indicator = document.createElement('div');
            indicator.className = `indicator ${index === 0 ? 'active' : ''}`;
            indicator.addEventListener('click', () => this.goToVideo(index));
            this.indicatorsContainer.appendChild(indicator);
        });
    }

    loadVideo(index) {
        if (index < 0 || index >= CONFIG.videos.length) return;
        
        const video = CONFIG.videos[index];
        this.video.src = video.url;
        this.video.load();
        
        // 更新指示器
        this.updateIndicators(index);
        this.currentIndex = index;
        
        // 视频加载完成后播放
        this.video.addEventListener('loadeddata', () => {
            if (this.isPlaying) {
                this.video.play().catch(e => {
                    console.log('视频自动播放失败:', e);
                    this.isPlaying = false;
                    this.updatePlayPauseButton();
                });
            }
        }, { once: true });

        // 视频播放结束时自动切换到下一个
        this.video.addEventListener('ended', () => {
            this.nextVideo();
        }, { once: true });
    }

    updateIndicators(activeIndex) {
        const indicators = this.indicatorsContainer.querySelectorAll('.indicator');
        indicators.forEach((indicator, index) => {
            indicator.classList.toggle('active', index === activeIndex);
        });
    }

    bindEvents() {
        // 播放/暂停按钮
        this.playPauseBtn.addEventListener('click', () => {
            this.togglePlayPause();
        });

        // 上一个视频
        this.prevBtn.addEventListener('click', () => {
            this.prevVideo();
        });

        // 下一个视频
        this.nextBtn.addEventListener('click', () => {
            this.nextVideo();
        });

        // 视频点击播放/暂停
        this.video.addEventListener('click', () => {
            this.togglePlayPause();
        });

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case ' ':
                    e.preventDefault();
                    this.togglePlayPause();
                    break;
                case 'ArrowLeft':
                    this.prevVideo();
                    break;
                case 'ArrowRight':
                    this.nextVideo();
                    break;
            }
        });

        // 鼠标悬停时暂停自动播放
        const videoContainer = document.querySelector('.video-container');
        videoContainer.addEventListener('mouseenter', () => {
            this.stopAutoPlay();
        });

        videoContainer.addEventListener('mouseleave', () => {
            if (this.isPlaying) {
                this.startAutoPlay();
            }
        });
    }

    togglePlayPause() {
        if (this.video.paused) {
            this.video.play().then(() => {
                this.isPlaying = true;
                this.updatePlayPauseButton();
                this.startAutoPlay();
            }).catch(e => {
                console.log('播放失败:', e);
            });
        } else {
            this.video.pause();
            this.isPlaying = false;
            this.updatePlayPauseButton();
            this.stopAutoPlay();
        }
    }

    updatePlayPauseButton() {
        const icon = this.playPauseBtn.querySelector('i');
        icon.className = this.isPlaying ? 'fas fa-pause' : 'fas fa-play';
    }

    prevVideo() {
        const prevIndex = (this.currentIndex - 1 + CONFIG.videos.length) % CONFIG.videos.length;
        this.goToVideo(prevIndex);
    }

    nextVideo() {
        const nextIndex = (this.currentIndex + 1) % CONFIG.videos.length;
        this.goToVideo(nextIndex);
    }

    goToVideo(index) {
        if (index === this.currentIndex) return;
        
        this.stopAutoPlay();
        this.loadVideo(index);
        
        if (this.isPlaying) {
            this.startAutoPlay();
        }
    }

    startAutoPlay() {
        this.stopAutoPlay();
        if (this.isPlaying) {
            this.autoPlayTimer = setTimeout(() => {
                this.nextVideo();
            }, CONFIG.autoPlayInterval);
        }
    }

    stopAutoPlay() {
        if (this.autoPlayTimer) {
            clearTimeout(this.autoPlayTimer);
            this.autoPlayTimer = null;
        }
    }

    preloadVideos() {
        // 预加载下一个视频以提高用户体验
        CONFIG.videos.forEach((video, index) => {
            if (index !== this.currentIndex) {
                const preloadVideo = document.createElement('video');
                preloadVideo.src = video.url;
                preloadVideo.preload = 'metadata';
                preloadVideo.style.display = 'none';
                document.body.appendChild(preloadVideo);
            }
        });
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 初始化视频轮播
    new VideoCarousel();

    // 添加页面交互效果
    addPageInteractions();
    
    // 添加加载动画
    addLoadingAnimations();
});

function addPageInteractions() {
    // 服务卡片悬停效果
    const serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // 侧边栏项目点击效果
    const sidebarItems = document.querySelectorAll('.sidebar-item');
    sidebarItems.forEach(item => {
        item.addEventListener('click', function() {
            sidebarItems.forEach(i => i.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // CTA按钮点击效果
    const ctaButton = document.querySelector('.cta-button');
    ctaButton.addEventListener('click', function() {
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 150);
        
        // 这里可以添加实际的跳转逻辑
        alert('欢迎体验清云互联 AI代码助手 CodeBuddy！');
    });
}

function addLoadingAnimations() {
    // 添加页面元素的渐入动画
    const animatedElements = document.querySelectorAll('.sidebar-item, .service-card, .main-text');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, { threshold: 0.1 });

    animatedElements.forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
        observer.observe(el);
    });
}

// 错误处理
window.addEventListener('error', (e) => {
    console.error('页面错误:', e.error);
});

// 视频加载错误处理
document.addEventListener('DOMContentLoaded', () => {
    const video = document.getElementById('mainVideo');
    video.addEventListener('error', (e) => {
        console.error('视频加载失败:', e);
        // 可以在这里添加备用视频或错误提示
    });
});
