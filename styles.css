/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* 导航栏样式 */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 0 20px;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 30px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: bold;
    color: #1976d2;
}

.logo i {
    font-size: 24px;
}

.nav-menu {
    display: flex;
    gap: 20px;
}

.nav-item {
    text-decoration: none;
    color: #666;
    font-size: 14px;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    gap: 4px;
}

.nav-item:hover {
    background: rgba(25, 118, 210, 0.1);
    color: #1976d2;
}

.hot-tag {
    background: #ff4444;
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 2px;
    font-weight: bold;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-box {
    display: flex;
    align-items: center;
    background: #f5f5f5;
    border-radius: 20px;
    padding: 8px 15px;
    gap: 8px;
}

.search-box input {
    border: none;
    background: none;
    outline: none;
    font-size: 14px;
    width: 150px;
}

.nav-actions {
    display: flex;
    gap: 15px;
    font-size: 14px;
    color: #666;
}

.nav-actions span {
    cursor: pointer;
    transition: color 0.3s ease;
}

.nav-actions span:hover {
    color: #1976d2;
}

/* 主要内容区域 */
.main-content {
    max-width: 1200px;
    margin: 40px auto;
    padding: 0 20px;
}

.content-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    margin-bottom: 80px;
}

/* 左侧内容 */
.left-content {
    display: flex;
    gap: 30px;
}

.sidebar {
    display: flex;
    flex-direction: column;
    gap: 15px;
    min-width: 200px;
}

.sidebar-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.sidebar-item:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sidebar-item.active {
    background: rgba(25, 118, 210, 0.1);
    color: #1976d2;
    border-left: 3px solid #1976d2;
}

.new-tag {
    background: #ff4444;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: bold;
}

.main-text {
    flex: 1;
}

.main-text h1 {
    font-size: 36px;
    font-weight: bold;
    color: white;
    margin-bottom: 20px;
    line-height: 1.2;
}

.main-text p {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 30px;
    line-height: 1.6;
}

.cta-button {
    background: #1976d2;
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.cta-button:hover {
    background: #1565c0;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
}

/* 右侧视频轮播 */
.right-content {
    position: relative;
}

.video-carousel {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.video-container {
    position: relative;
    width: 100%;
    height: 400px;
    overflow: hidden;
}

#mainVideo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 20px;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.video-container:hover .video-overlay {
    opacity: 1;
}

.video-controls {
    display: flex;
    gap: 15px;
}

.control-btn {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: #333;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: white;
    transform: scale(1.1);
}

.video-indicators {
    display: flex;
    justify-content: center;
    gap: 10px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.9);
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ddd;
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background: #1976d2;
    transform: scale(1.2);
}

/* 服务卡片区域 */
.services-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
    margin-top: 40px;
}

.service-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px 20px;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    background: white;
}

.service-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 24px;
}

.service-icon.blue {
    background: rgba(25, 118, 210, 0.1);
    color: #1976d2;
}

.service-card h3 {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
}

.service-card p {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .content-container {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .left-content {
        flex-direction: column;
        gap: 20px;
    }
    
    .main-text h1 {
        font-size: 28px;
    }
    
    .services-section {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .video-container {
        height: 250px;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 10px;
    }
    
    .main-content {
        padding: 0 15px;
    }
    
    .main-text h1 {
        font-size: 24px;
    }
    
    .services-section {
        grid-template-columns: 1fr;
    }
}
