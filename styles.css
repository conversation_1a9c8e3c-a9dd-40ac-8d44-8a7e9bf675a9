/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: #f5f7fa;
    min-height: 100vh;
    color: #333;
}

/* 导航栏样式 */
.navbar {
    background: white;
    border-bottom: 1px solid #e8eaed;
    padding: 0 20px;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 1px 6px rgba(32,33,36,.28);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 40px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 500;
    color: #1a73e8;
}

.logo i {
    font-size: 20px;
    color: #1a73e8;
}

.nav-menu {
    display: flex;
    gap: 32px;
}

.nav-item {
    text-decoration: none;
    color: #5f6368;
    font-size: 14px;
    padding: 8px 0;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 400;
}

.nav-item:hover {
    color: #1a73e8;
}

.hot-tag {
    background: #ea4335;
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 2px;
    font-weight: 500;
    margin-left: 4px;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 24px;
}

.search-box {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border: 1px solid #dadce0;
    border-radius: 24px;
    padding: 8px 16px;
    gap: 8px;
    width: 240px;
}

.search-box i {
    color: #9aa0a6;
    font-size: 16px;
}

.search-box input {
    border: none;
    background: none;
    outline: none;
    font-size: 14px;
    flex: 1;
    color: #3c4043;
}

.search-box input::placeholder {
    color: #9aa0a6;
}

.nav-actions {
    display: flex;
    gap: 20px;
    font-size: 13px;
    color: #5f6368;
}

.nav-actions span {
    cursor: pointer;
    transition: color 0.3s ease;
    padding: 8px 12px;
    border-radius: 4px;
}

.nav-actions span:hover {
    color: #1a73e8;
    background: #f8f9fa;
}

/* 英雄区域样式 */
.hero-section {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%);
    padding: 60px 0 100px;
    position: relative;
    overflow: hidden;
}

.hero-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 2;
}

.hero-left {
    flex: 1;
    max-width: 600px;
}

.hero-breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;
    font-size: 14px;
    color: #5f6368;
    flex-wrap: wrap;
}

.hot-badge {
    background: #ea4335;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.hero-sidebar {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 30px;
}

.sidebar-item {
    font-size: 13px;
    color: #5f6368;
    padding: 4px 0;
}

.hero-title {
    font-size: 42px;
    font-weight: 500;
    margin-bottom: 20px;
    color: #202124;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 16px;
    color: #5f6368;
    margin-bottom: 40px;
    line-height: 1.6;
}

.cta-button {
    background: #1a73e8;
    color: white;
    padding: 12px 32px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.cta-button:hover {
    background: #1557b0;
    box-shadow: 0 2px 8px rgba(26, 115, 232, 0.4);
}

.hero-right {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.hero-illustration {
    width: 400px;
    height: 300px;
    position: relative;
}

.pyramid {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 150px;
}

.pyramid-layer {
    position: absolute;
    border-radius: 8px;
}

.pyramid-layer:nth-child(1) {
    width: 60px;
    height: 30px;
    background: #1a73e8;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
}

.pyramid-layer:nth-child(2) {
    width: 120px;
    height: 40px;
    background: #4285f4;
    top: 40px;
    left: 50%;
    transform: translateX(-50%);
}

.pyramid-layer:nth-child(3) {
    width: 180px;
    height: 50px;
    background: #64b5f6;
    top: 90px;
    left: 50%;
    transform: translateX(-50%);
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-element {
    position: absolute;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
    padding: 8px 12px;
    font-size: 12px;
    color: #5f6368;
    font-weight: 500;
}

.floating-element:nth-child(1) {
    top: 20px;
    right: 20px;
}

.floating-element:nth-child(2) {
    bottom: 40px;
    left: 20px;
}

.floating-element:nth-child(3) {
    top: 60px;
    left: 0;
}

/* 主要内容区域 */
.main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 60px 20px;
}

.features-header {
    text-align: center;
    margin-bottom: 60px;
}

.features-header h2 {
    font-size: 28px;
    font-weight: 500;
    color: #202124;
    margin-bottom: 16px;
}

.features-header p {
    font-size: 16px;
    color: #5f6368;
    line-height: 1.6;
}

.features-header a {
    color: #1a73e8;
    text-decoration: none;
}

.features-header a:hover {
    text-decoration: underline;
}

/* 特性网格样式 */
.features-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 20px;
    margin-bottom: 80px;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 24px 16px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e8eaed;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.feature-item:hover {
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.feature-item i {
    font-size: 24px;
    color: #1a73e8;
}

.feature-item span {
    font-size: 13px;
    color: #5f6368;
    font-weight: 500;
    line-height: 1.4;
}

/* 响应式特性网格 */
@media (max-width: 1200px) {
    .features-grid {
        grid-template-columns: repeat(6, 1fr);
    }
}

@media (max-width: 900px) {
    .features-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 600px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }

    .feature-item {
        padding: 20px 12px;
    }
}

/* 服务卡片区域 */
.services-section {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
    margin-top: 40px;
}

.service-card {
    background: white;
    padding: 24px 20px;
    border-radius: 8px;
    border: 1px solid #e8eaed;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.service-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.service-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 20px;
}

.service-icon.blue {
    background: rgba(26, 115, 232, 0.1);
    color: #1a73e8;
}

.service-card h3 {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #202124;
}

.service-card p {
    font-size: 13px;
    color: #5f6368;
    line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .hero-container {
        flex-direction: column;
        gap: 40px;
        text-align: center;
    }

    .hero-left {
        max-width: 100%;
    }

    .hero-title {
        font-size: 36px;
    }

    .services-section {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hero-title {
        font-size: 28px;
    }

    .hero-subtitle {
        font-size: 14px;
    }

    .features-header h2 {
        font-size: 24px;
    }

    .services-section {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }

    .hero-illustration {
        width: 300px;
        height: 200px;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 10px;
    }

    .main-content {
        padding: 40px 15px;
    }

    .hero-title {
        font-size: 24px;
    }

    .services-section {
        grid-template-columns: 1fr;
    }

    .hero-breadcrumb {
        font-size: 12px;
    }

    .search-box {
        width: 180px;
    }
}
